using LibraryAPI.AppDataContext;
using LibraryAPI.Contracts.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace LibraryAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserController : ControllerBase
    {
        private readonly LibraryDbContext _dbContext;

        public UserController(LibraryDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        [HttpGet("profile")]
        public async Task<IActionResult> GetProfile()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
                return Unauthorized(new { message = "User ID not found in token." });
                
            var user = await _dbContext.Users.FindAsync(Guid.Parse(userId));
            
            if (user == null)
                return NotFound();
                
            return Ok(new UserResponse
            {
                Id = user.Id,
                Name = user.Name,
                Email = user.Email,
                Role = user.Role
            });
        }

        [HttpPut("profile")]
        public async Task<IActionResult> UpdateProfile([FromBody] UpdateProfileRequest request)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
                return Unauthorized(new { message = "User ID not found in token." });
                
            var user = await _dbContext.Users.FindAsync(Guid.Parse(userId));
            
            if (user == null)
                return NotFound();
                
            // Update user properties
            user.Name = request.Name ?? user.Name;
            
            // If email is being changed, check if it's already in use
            if (request.Email != null && request.Email != user.Email)
            {
                var emailExists = await _dbContext.Users.AnyAsync(u => u.Email == request.Email);
                if (emailExists)
                    return BadRequest(new { message = "Email already in use." });
                    
                user.Email = request.Email;
            }
            
            // If password is being changed, hash it
            if (!string.IsNullOrEmpty(request.Password))
            {
                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password);
            }
            
            try
            {
                await _dbContext.SaveChangesAsync();
                
                return Ok(new UserResponse
                {
                    Id = user.Id,
                    Name = user.Name,
                    Email = user.Email,
                    Role = user.Role
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = $"Error updating profile: {ex.Message}" });
            }
        }
    }
}
